"use client";

import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Users,
  Target,
  Clock,
  Globe,
  Award,
} from "lucide-react";

// Avencion General FAQs
const avencionFAQs = [
  {
    id: 1,
    question: "In what areas do you provide management consulting?",
    answer: "Our associate consultants specialize in a range of business areas. These include strategic planning, issues resolution, culture assessment, board effectiveness, retention strategies, ICT4D implementation, capacity building, data management, and public-private partnership development.",
    category: "Services"
  },
  {
    id: 2,
    question: "In which countries do you provide consulting services?",
    answer: "We provide consulting services across 11 African countries including Tanzania, Mozambique, Burundi, South Africa, Zambia, Zimbabwe, Uganda, Malawi, Kenya, Ghana, and Rwanda. We have offices in Lusaka, Zambia and Houston, Texas to serve our global clients.",
    category: "Coverage"
  },
  {
    id: 3,
    question: "How is a consulting project started and organized?",
    answer: "Our consulting projects begin with an initial consultation to understand your needs and objectives. We then develop a customized proposal outlining the scope, timeline, deliverables, and investment required. Once approved, we assign a dedicated project team and establish clear communication protocols and milestone reviews.",
    category: "Process"
  },
  {
    id: 4,
    question: "Do you do fixed price or time and materials contracts?",
    answer: "We offer both fixed price and time and materials contracts depending on the nature and scope of the project. Fixed price contracts work well for clearly defined projects with specific deliverables, while time and materials contracts are better suited for ongoing advisory work or projects with evolving requirements.",
    category: "Pricing"
  },
  {
    id: 5,
    question: "Do you offer volume or loyalty discounts?",
    answer: "Yes, we offer preferential pricing for long-term partnerships and multi-project engagements. We believe in building lasting relationships with our clients and recognize their continued trust with competitive pricing structures for ongoing work.",
    category: "Pricing"
  },
  {
    id: 6,
    question: "What problems does business consulting typically solve?",
    answer: "Business consulting helps solve challenges such as organizational inefficiencies, strategic planning gaps, technology implementation issues, capacity building needs, partnership development, data management problems, and operational optimization. We focus on sustainable solutions that create lasting impact.",
    category: "Solutions"
  },
  {
    id: 7,
    question: "How is the scope of a consulting project determined?",
    answer: "Project scope is determined through collaborative discussions with stakeholders, comprehensive needs assessment, analysis of current state versus desired outcomes, and consideration of available resources and timelines. We ensure all parties have clear expectations before project commencement.",
    category: "Process"
  },
  {
    id: 8,
    question: "How long does a business consulting project last?",
    answer: "Project duration varies significantly based on scope and complexity. Short-term assessments may take 2-4 weeks, while comprehensive organizational transformations can span 12-24 months. We provide realistic timelines during the proposal phase and maintain flexibility for project adjustments.",
    category: "Timeline"
  }
];

// DOT Project FAQs
const dotFAQs = [
  {
    id: 1,
    question: "What is the Going Beyond Beyond Project?",
    answer: "The Going Beyond – Partnering for a Youth-Led Future project is a partnership between DOT and the Mastercard Foundation that aims to scale DOT's evidence-based, peer-to-peer model and will be executed in four African countries – Côte d'Ivoire, Malawi, Zambia, and Tanzania. Over five years, and with the support of DOT regional hubs in Africa, the Going Beyond project will progressively transfer the responsibility for delivering DOT's programs and equip youth as leaders of the future.",
    category: "Project Overview"
  },
  {
    id: 2,
    question: "What are the project goals?",
    answer: "The project aims to scale DOT's evidence-based, peer-to-peer model across four African countries, progressively transfer program delivery responsibility to local youth, and equip young people as leaders of the future through comprehensive capacity building and mentorship programs.",
    category: "Project Overview"
  },
  {
    id: 3,
    question: "What are the expected outcomes and impacts of the project on youth-led entrepreneurship and economic development?",
    answer: "The project expects to significantly boost youth-led entrepreneurship by providing direct support to young MSME owners, creating sustainable economic opportunities, and building local capacity for ongoing program delivery. The impact includes increased youth employment, enhanced entrepreneurial skills, and strengthened local economies across the four target countries.",
    category: "Impact & Outcomes"
  },
  {
    id: 4,
    question: "How does this partnership align with the Mastercard Foundation's Young Africa Works strategy?",
    answer: "This partnership directly supports the Young Africa Works strategy by focusing on youth employment, entrepreneurship development, and economic empowerment across Africa. The project's emphasis on peer-to-peer learning and youth leadership development aligns perfectly with the Foundation's commitment to enabling young people to access dignified and fulfilling work.",
    category: "Partnership"
  },
  {
    id: 5,
    question: "How will the initiative measure its success, and what indicators will be used to track progress?",
    answer: "Success will be measured through various indicators including the number of youth trained and employed, business growth metrics for supported MSMEs, leadership development outcomes, and the successful transfer of program delivery to local youth organizations. Regular monitoring and evaluation will track both quantitative and qualitative progress indicators.",
    category: "Monitoring & Evaluation"
  },
  {
    id: 6,
    question: "What are the timelines of the Going Beyond Project?",
    answer: "The Going Beyond Project is designed as a five-year initiative, with progressive phases that gradually transfer responsibility to local youth leaders and organizations. The timeline includes initial setup and training phases, implementation and scaling phases, and final transition to youth-led program delivery.",
    category: "Timeline"
  },
  {
    id: 7,
    question: "Who are the participants in the project?",
    answer: "Participants include Youth Leaders who will be trained to deliver programs, Youth Peers who are young MSME owners receiving support, local Youth-Led Organizations (YLOs) and Youth-Serving Organizations (YSOs), and members of the Impact Makers Advisory Board who provide strategic guidance.",
    category: "Participants"
  },
  {
    id: 8,
    question: "How will the project engage with local Youth-Led Organizations (YLOs) and Youth-Serving Organizations (YSOs)?",
    answer: "The project will work closely with YLOs and YSOs as key implementation partners, providing them with capacity building support, resources, and training to effectively deliver programs in their communities. These organizations will play crucial roles in identifying participants, delivering services, and ensuring program sustainability.",
    category: "Partnerships"
  },
  {
    id: 9,
    question: "Who are the Youth Leaders in the Going Beyond Project?",
    answer: "Youth Leaders are carefully selected young individuals who will be trained to deliver DOT's programs in their communities. They serve as mentors, trainers, and program coordinators, playing a central role in the peer-to-peer learning model that is core to the project's approach.",
    category: "Youth Leaders"
  },
  {
    id: 10,
    question: "How will Youth Leaders be selected and trained for their roles within the project?",
    answer: "Youth Leaders will be selected through a comprehensive process that evaluates their leadership potential, community engagement, and commitment to youth development. Training will include program delivery methodologies, mentorship skills, business development knowledge, and ongoing support throughout their tenure.",
    category: "Youth Leaders"
  },
  {
    id: 11,
    question: "What opportunities are available for Youth Leaders to participate in the project?",
    answer: "Youth Leaders have opportunities to lead program delivery, mentor young entrepreneurs, participate in capacity building workshops, engage in peer-to-peer learning networks, and contribute to the strategic development of youth-led initiatives in their communities.",
    category: "Youth Leaders"
  },
  {
    id: 12,
    question: "Are Youth Leaders Paid?",
    answer: "Yes, Youth Leaders receive compensation for their roles in the project. The payment structure is designed to recognize their valuable contribution while ensuring the sustainability of the program model.",
    category: "Youth Leaders"
  },
  {
    id: 13,
    question: "Who are the Youth Peers in the Going Beyond Project?",
    answer: "Youth Peers are young MSME (Micro, Small, and Medium Enterprise) owners who participate in the project as beneficiaries. They receive support, training, and mentorship to grow their businesses and develop their entrepreneurial skills through the peer-to-peer learning model.",
    category: "Participants"
  },
  {
    id: 14,
    question: "What types of activities or opportunities are available for Youth Peers (young MSME owners) to participate in?",
    answer: "Youth Peers can participate in business development workshops, peer mentoring sessions, networking events, skills training programs, access to market linkages, and ongoing support for business growth and sustainability.",
    category: "Participants"
  },
  {
    id: 15,
    question: "What avenues are available for Youth Peers to access financial support or resources for their entrepreneurial ventures?",
    answer: "The project provides various pathways to financial support including connections to microfinance institutions, access to grant opportunities, business development support that improves creditworthiness, and linkages to impact investors and funding networks.",
    category: "Financial Support"
  },
  {
    id: 16,
    question: "What is the Impact Makers Advisory Board?",
    answer: "The Impact Makers Advisory Board is a strategic governance body composed of experienced professionals, youth representatives, and stakeholders who provide guidance, oversight, and strategic direction to ensure the project achieves its objectives and maintains its focus on youth empowerment.",
    category: "Governance"
  },
  {
    id: 17,
    question: "Are there any fees associated with the training programs?",
    answer: "No, the training programs provided through the Going Beyond project are free of charge for all participants. The project is designed to remove financial barriers and ensure equitable access to capacity building opportunities for young people.",
    category: "Program Details"
  },
  {
    id: 18,
    question: "How will the project ensure the inclusion and representation of marginalized or vulnerable youth?",
    answer: "The project has specific strategies to ensure inclusive participation, including targeted outreach to marginalized communities, accessibility accommodations, culturally sensitive programming, and partnerships with organizations that serve vulnerable youth populations.",
    category: "Inclusion"
  },
  {
    id: 19,
    question: "How will we safeguard the young people participating in the project?",
    answer: "Comprehensive safeguarding measures include background checks for all staff and volunteers, clear safeguarding policies and procedures, regular training on child protection, safe reporting mechanisms, and ongoing monitoring to ensure participant safety and wellbeing.",
    category: "Safeguarding"
  },
  {
    id: 20,
    question: "What role will technology play in supporting the project's objectives and activities?",
    answer: "Technology will be integral to program delivery, including digital learning platforms, mobile applications for peer networking, online mentorship tools, digital financial services integration, and data management systems for monitoring and evaluation.",
    category: "Technology"
  },
  {
    id: 21,
    question: "How will the project address challenges related to access to technology and digital literacy among youth participants?",
    answer: "The project includes digital literacy training components, partnerships with technology providers for device access, offline program alternatives, and gradual technology integration that builds participants' digital skills alongside program participation.",
    category: "Technology"
  },
  {
    id: 22,
    question: "How will the project foster collaboration and knowledge sharing among stakeholders?",
    answer: "Collaboration will be fostered through regular stakeholder meetings, knowledge sharing platforms, cross-country learning exchanges, joint planning sessions, and the creation of communities of practice that enable ongoing collaboration and learning.",
    category: "Collaboration"
  },
  {
    id: 23,
    question: "How can I find more information about Going Beyond?",
    answer: "For more information about the Going Beyond project, you can contact Avencion Limited directly through our website, attend our information sessions, follow our social media channels, or reach out to our project team for specific inquiries about participation or partnership opportunities.",
    category: "Information"
  }
];

const avencionCategories = ["All", "Services", "Coverage", "Process", "Pricing", "Solutions", "Timeline"];
const dotCategories = [
  "All",
  "Project Overview",
  "Impact & Outcomes",
  "Partnership",
  "Youth Leaders",
  "Participants",
  "Financial Support",
  "Technology",
  "Program Details",
  "Inclusion",
  "Safeguarding",
  "Governance",
  "Collaboration",
  "Information"
];

export default function FAQPage() {
  const [activeAvencionCategory, setActiveAvencionCategory] = useState("All");
  const [activeDotCategory, setActiveDotCategory] = useState("All");
  const [openAvencionItems, setOpenAvencionItems] = useState<number[]>([1]); // First item open by default
  const [openDotItems, setOpenDotItems] = useState<number[]>([]);

  const heroRef = useRef(null);
  const avencionFaqRef = useRef(null);
  const dotFaqRef = useRef(null);
  const heroInView = useInView(heroRef, { once: true });
  const avencionFaqInView = useInView(avencionFaqRef, { once: true, amount: 0.3 });
  const dotFaqInView = useInView(dotFaqRef, { once: true, amount: 0.3 });

  const filteredAvencionFAQs = activeAvencionCategory === "All"
    ? avencionFAQs
    : avencionFAQs.filter(faq => faq.category === activeAvencionCategory);

  const filteredDotFAQs = activeDotCategory === "All"
    ? dotFAQs
    : dotFAQs.filter(faq => faq.category === activeDotCategory);

  const toggleAvencionItem = (id: number) => {
    setOpenAvencionItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const toggleDotItem = (id: number) => {
    setOpenDotItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1531482615713-2afd69097998?w=1920&h=1080&fit=crop"
            alt="FAQ - Going Beyond Project"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
        </div>

        {/* Decorative Elements */}
        <div
          className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />
        <div
          className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />

        <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-center"
          >
            <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
             Most Popular Questions
            </span>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              Frequently Asked{" "}
              <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
                Questions
              </span>
            </h1>
            <p className="mx-auto max-w-4xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
              We help you see the world differently, discover opportunities you may never have imagined and achieve results that bridge what is with what can be.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
            >
              <Link
                href="#faqs"
                className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
              >
                Avencion FAQs
              </Link>
              <Link
                href="#dot-faqs"
                className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
              >
                DOT Project FAQs
              </Link>
            </motion.div>

            {/* Company Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              className="grid grid-cols-2 gap-8 md:grid-cols-4"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">350+</div>
                <div className="text-sm text-gray-300 md:text-base">Clients Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">11</div>
                <div className="text-sm text-gray-300 md:text-base">African Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">15+</div>
                <div className="text-sm text-gray-300 md:text-base">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">24hrs</div>
                <div className="text-sm text-gray-300 md:text-base">Response Time</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Avencion FAQ Section */}
      <section id="faqs" ref={avencionFaqRef} className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={avencionFaqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
              Avencion Limited FAQs
            </h2>
            <p className="mx-auto max-w-3xl text-lg text-gray-600">
              Find answers to common questions about our consulting services and approach.
            </p>
          </motion.div>

          {/* Category Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={avencionFaqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            className="mb-12 flex flex-wrap justify-center gap-2"
          >
            {avencionCategories.map((category) => (
              <button
                key={category}
                type="button"
                onClick={() => setActiveAvencionCategory(category)}
                className={`rounded-full px-6 py-2 text-sm font-medium transition-all duration-300 ${
                  activeAvencionCategory === category
                    ? "bg-blue-500 text-white shadow-lg"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {category}
              </button>
            ))}
          </motion.div>

          {/* Avencion FAQ Items */}
          <div className="mx-auto max-w-4xl">
            <div className="space-y-4">
              {filteredAvencionFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={avencionFaqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.4, ease: "easeOut" }}
                  className="relative overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg"
                >
                  <BorderBeam
                    duration={8 + index}
                    size={300}
                    className="from-transparent via-[#92b2f8]/40 to-transparent"
                  />

                  <button
                    type="button"
                    onClick={() => toggleAvencionItem(faq.id)}
                    className="w-full p-6 text-left transition-all duration-300 hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <span className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-600">
                            {faq.category}
                          </span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 pr-4">
                          {faq.question}
                        </h3>
                      </div>
                      <div className="flex-shrink-0">
                        {openAvencionItems.includes(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-blue-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </button>

                  {openAvencionItems.includes(faq.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className="border-t border-gray-100 px-6 pb-6"
                    >
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* DOT Project FAQ Section */}
      <section id="dot-faqs" ref={dotFaqRef} className="relative w-full overflow-hidden py-20"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        }}>
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={dotFaqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
              Going Beyond - DOT Project FAQs
            </h2>
            <p className="mx-auto max-w-3xl text-lg text-gray-600">
              Find answers to common questions about the Going Beyond – Partnering for a Youth-Led Future project.
            </p>
          </motion.div>

          {/* DOT Category Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={dotFaqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            className="mb-12 flex flex-wrap justify-center gap-2"
          >
            {dotCategories.map((category) => (
              <button
                key={category}
                type="button"
                onClick={() => setActiveDotCategory(category)}
                className={`rounded-full px-6 py-2 text-sm font-medium transition-all duration-300 ${
                  activeDotCategory === category
                    ? "bg-blue-500 text-white shadow-lg"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {category}
              </button>
            ))}
          </motion.div>

          {/* DOT FAQ Items */}
          <div className="mx-auto max-w-4xl">
            <div className="space-y-4">
              {filteredDotFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={dotFaqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.4, ease: "easeOut" }}
                  className="relative overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg"
                >
                  <BorderBeam
                    duration={8 + index}
                    size={300}
                    className="from-transparent via-[#92b2f8]/40 to-transparent"
                  />

                  <button
                    type="button"
                    onClick={() => toggleDotItem(faq.id)}
                    className="w-full p-6 text-left transition-all duration-300 hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <span className="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-600">
                            {faq.category}
                          </span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 pr-4">
                          {faq.question}
                        </h3>
                      </div>
                      <div className="flex-shrink-0">
                        {openDotItems.includes(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-green-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </button>

                  {openDotItems.includes(faq.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className="border-t border-gray-100 px-6 pb-6"
                    >
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
