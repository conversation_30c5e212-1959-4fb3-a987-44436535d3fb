import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

// Verify reCAPTC<PERSON>
async function verifyRecaptcha(token: string): Promise<boolean> {
  // Use test secret key for development, production key for production
  const secretKey = process.env.NODE_ENV === 'development'
    ? "6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe" // Test secret key for localhost
    : "6LeBRU8rAAAAACrpc9lWesejYpEfb_bW-zeU95D8"; // Your production secret key

  if (!secretKey) {
    console.error('RECAPTCHA_SECRET_KEY not configured');
    return false;
  }

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${secretKey}&response=${token}`,
    });

    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('Error verifying reCAPTCHA:', error);
    return false;
  }
}

// Create nodemailer transporter
function createTransporter() {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, company, phone, subject, message, captcha } = body;

    // Validate required fields
    if (!name || !email || !subject || !message || !captcha) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify reCAPTCHA
    const isValidCaptcha = await verifyRecaptcha(captcha);
    if (!isValidCaptcha) {
      return NextResponse.json(
        { error: 'Invalid CAPTCHA. Please try again.' },
        { status: 400 }
      );
    }

    // Create transporter
    const transporter = createTransporter();

    // Email content
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">New Contact Form Submission</h1>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <div style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Contact Details</h2>
            
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555; width: 120px;">Name:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #333;">${name}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Email:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #333;">${email}</td>
              </tr>
              ${company ? `
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Company:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #333;">${company}</td>
              </tr>
              ` : ''}
              ${phone ? `
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Phone:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #333;">${phone}</td>
              </tr>
              ` : ''}
              <tr>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">Subject:</td>
                <td style="padding: 10px 0; border-bottom: 1px solid #eee; color: #333;">${subject}</td>
              </tr>
            </table>
            
            <h3 style="color: #333; margin-top: 25px; margin-bottom: 15px; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Message</h3>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #667eea;">
              <p style="margin: 0; line-height: 1.6; color: #333; white-space: pre-wrap;">${message}</p>
            </div>
          </div>
        </div>
        
        <div style="background: #333; padding: 20px; text-align: center;">
          <p style="color: #ccc; margin: 0; font-size: 14px;">
            This message was sent from the Avencion Limited contact form<br>
            <strong>Received:</strong> ${new Date().toLocaleString()}
          </p>
        </div>
      </div>
    `;

    const textContent = `
New Contact Form Submission

Name: ${name}
Email: ${email}
${company ? `Company: ${company}\n` : ''}${phone ? `Phone: ${phone}\n` : ''}Subject: ${subject}

Message:
${message}

---
This message was sent from the Avencion Limited contact form
Received: ${new Date().toLocaleString()}
    `;

    // Email options
    const mailOptions = {
      from: `"Avencion Contact Form" <${process.env.SMTP_USER}>`,
      to: process.env.CONTACT_EMAIL || '<EMAIL>',
      replyTo: email,
      subject: `Contact Form: ${subject}`,
      text: textContent,
      html: htmlContent,
    };

    // Send email
    await transporter.sendMail(mailOptions);

    // Send auto-reply to user
    const autoReplyOptions = {
      from: `"Avencion Limited" <${process.env.SMTP_USER}>`,
      to: email,
      subject: 'Thank you for contacting Avencion Limited',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Thank You for Contacting Us!</h1>
          </div>
          
          <div style="padding: 30px; background: #f8f9fa;">
            <div style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
              <p style="color: #333; font-size: 16px; line-height: 1.6;">Dear ${name},</p>
              
              <p style="color: #333; font-size: 16px; line-height: 1.6;">
                Thank you for reaching out to Avencion Limited. We have received your message regarding "<strong>${subject}</strong>" and appreciate your interest in our services.
              </p>
              
              <p style="color: #333; font-size: 16px; line-height: 1.6;">
                Our team will review your inquiry and respond within 24 hours during business days (Monday-Friday, 8:00-17:00). 
                We're excited about the opportunity to work with you and help bring your vision to reality.
              </p>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 4px solid #667eea; margin: 20px 0;">
                <h3 style="color: #333; margin-top: 0;">Your Message Summary:</h3>
                <p style="margin: 5px 0; color: #666;"><strong>Subject:</strong> ${subject}</p>
                <p style="margin: 5px 0; color: #666;"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
              </div>
              
              <p style="color: #333; font-size: 16px; line-height: 1.6;">
                In the meantime, feel free to explore our website to learn more about our services and recent projects.
              </p>
              
              <p style="color: #333; font-size: 16px; line-height: 1.6;">
                Best regards,<br>
                <strong>The Avencion Limited Team</strong>
              </p>
            </div>
          </div>
          
          <div style="background: #333; padding: 20px; text-align: center;">
            <p style="color: #ccc; margin: 0; font-size: 14px;">
              <strong>Avencion Limited</strong><br>
              Email: <EMAIL> | Phone: +260 960 638 188 (Zambia) | ****** 676 7171 (USA)<br>
              2 Chifumbule Road, Woodlands, Lusaka, Zambia
            </p>
          </div>
        </div>
      `,
    };

    await transporter.sendMail(autoReplyOptions);

    return NextResponse.json(
      { message: 'Email sent successfully' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error sending email:', error);
    return NextResponse.json(
      { error: 'Failed to send email. Please try again later.' },
      { status: 500 }
    );
  }
}
