"use client";

import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Users,
  Target,
  Clock,
  Globe,
  Award,
} from "lucide-react";

// Avencion General FAQs
const avencionFAQs = [
  {
    id: 1,
    question: "In what areas do you provide management consulting?",
    answer: "Our associate consultants specialize in a range of business areas. These include strategic planning, issues resolution, culture assessment, board effectiveness, retention strategies, ICT4D implementation, capacity building, data management, and public-private partnership development.",
    category: "Services"
  },
  {
    id: 2,
    question: "In which countries do you provide consulting services?",
    answer: "We provide consulting services across 11 African countries including Tanzania, Mozambique, Burundi, South Africa, Zambia, Zimbabwe, Uganda, Malawi, Kenya, Ghana, and Rwanda. We have offices in Lusaka, Zambia and Houston, Texas to serve our global clients.",
    category: "Coverage"
  },
  {
    id: 3,
    question: "How is a consulting project started and organized?",
    answer: "Our consulting projects begin with an initial consultation to understand your needs and objectives. We then develop a customized proposal outlining the scope, timeline, deliverables, and investment required. Once approved, we assign a dedicated project team and establish clear communication protocols and milestone reviews.",
    category: "Process"
  },
  {
    id: 4,
    question: "Do you do fixed price or time and materials contracts?",
    answer: "We offer both fixed price and time and materials contracts depending on the nature and scope of the project. Fixed price contracts work well for clearly defined projects with specific deliverables, while time and materials contracts are better suited for ongoing advisory work or projects with evolving requirements.",
    category: "Pricing"
  },
  {
    id: 5,
    question: "Do you offer volume or loyalty discounts?",
    answer: "Yes, we offer preferential pricing for long-term partnerships and multi-project engagements. We believe in building lasting relationships with our clients and recognize their continued trust with competitive pricing structures for ongoing work.",
    category: "Pricing"
  },
  {
    id: 6,
    question: "What problems does business consulting typically solve?",
    answer: "Business consulting helps solve challenges such as organizational inefficiencies, strategic planning gaps, technology implementation issues, capacity building needs, partnership development, data management problems, and operational optimization. We focus on sustainable solutions that create lasting impact.",
    category: "Solutions"
  },
  {
    id: 7,
    question: "How is the scope of a consulting project determined?",
    answer: "Project scope is determined through collaborative discussions with stakeholders, comprehensive needs assessment, analysis of current state versus desired outcomes, and consideration of available resources and timelines. We ensure all parties have clear expectations before project commencement.",
    category: "Process"
  },
  {
    id: 8,
    question: "How long does a business consulting project last?",
    answer: "Project duration varies significantly based on scope and complexity. Short-term assessments may take 2-4 weeks, while comprehensive organizational transformations can span 12-24 months. We provide realistic timelines during the proposal phase and maintain flexibility for project adjustments.",
    category: "Timeline"
  }
];

const avencionCategories = ["All", "Services", "Coverage", "Process", "Pricing", "Solutions", "Timeline"];

export default function AvencionFAQPage() {
  const [activeCategory, setActiveCategory] = useState("All");
  const [openItems, setOpenItems] = useState<number[]>([1]); // First item open by default
  
  const heroRef = useRef(null);
  const faqRef = useRef(null);
  const heroInView = useInView(heroRef, { once: true });
  const faqInView = useInView(faqRef, { once: true, amount: 0.1, margin: "100px" });

  const filteredFAQs = activeCategory === "All" 
    ? avencionFAQs 
    : avencionFAQs.filter(faq => faq.category === activeCategory);

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1531482615713-2afd69097998?w=1920&h=1080&fit=crop"
            alt="Avencion Limited FAQs"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
        </div>

        {/* Decorative Elements */}
        <div
          className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />
        <div
          className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />

        <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-center"
          >
            <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
             Avencion Limited - Most Popular Questions
            </span>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              Avencion{" "}
              <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
                FAQs
              </span>
            </h1>
            <p className="mx-auto max-w-4xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
              We help you see the world differently, discover opportunities you may never have imagined and achieve results that bridge what is with what can be.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
            >
              <Link
                href="#faqs"
                className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
              >
                Explore FAQs
              </Link>
              <Link
                href="/faq/dot"
                className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
              >
                DOT Project FAQs
              </Link>
            </motion.div>

            {/* Company Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              className="grid grid-cols-2 gap-8 md:grid-cols-4"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">350+</div>
                <div className="text-sm text-gray-300 md:text-base">Clients Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">11</div>
                <div className="text-sm text-gray-300 md:text-base">African Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">15+</div>
                <div className="text-sm text-gray-300 md:text-base">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">24hrs</div>
                <div className="text-sm text-gray-300 md:text-base">Response Time</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faqs" ref={faqRef} className="relative w-full overflow-hidden bg-white py-12">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-8 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
              Avencion Limited FAQs
            </h2>
            <p className="mx-auto max-w-3xl text-lg text-gray-600">
              Find answers to common questions about our consulting services and approach.
            </p>
          </motion.div>

          {/* Category Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            className="mb-8 flex flex-wrap justify-center gap-2"
          >
            {avencionCategories.map((category) => (
              <button
                key={category}
                type="button"
                onClick={() => setActiveCategory(category)}
                className={`rounded-full px-6 py-2 text-sm font-medium transition-all duration-300 ${
                  activeCategory === category
                    ? "bg-blue-500 text-white shadow-lg"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {category}
              </button>
            ))}
          </motion.div>

          {/* FAQ Items */}
          <div className="mx-auto max-w-4xl">
            <div className="space-y-4">
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.4, ease: "easeOut" }}
                  className="relative overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg"
                >
                  <BorderBeam
                    duration={8 + index}
                    size={300}
                    className="from-transparent via-[#92b2f8]/40 to-transparent"
                  />
                  
                  <button
                    type="button"
                    onClick={() => toggleItem(faq.id)}
                    className="w-full p-6 text-left transition-all duration-300 hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <span className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-600">
                            {faq.category}
                          </span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 pr-4">
                          {faq.question}
                        </h3>
                      </div>
                      <div className="flex-shrink-0">
                        {openItems.includes(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-blue-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </button>
                  
                  {openItems.includes(faq.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className="border-t border-gray-100 px-6 pb-6"
                    >
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
