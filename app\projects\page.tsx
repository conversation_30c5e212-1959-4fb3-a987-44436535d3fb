"use client";

import { motion, useInView, AnimatePresence } from "framer-motion";
import { useRef, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import { Spotlight } from "@/components/ui/spotlight";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  Globe,
  Users,
  Database,
  Lightbulb,
  Building,
  Heart,
  Target,
  Award,
  ExternalLink,
  Calendar,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

const projects = [
  {
    id: 1,
    title: "REMS (Routine Efficiency Monitoring System)",
    category: "ICT4D",
    description: "An innovative e-health platform designed to improve the efficiency of HIV/AIDS care delivery across Zambia, supported by the Bill & Melinda Gates Foundation.",
    fullDescription: "REMS is an innovative e-health platform developed by Avencion in partnership with FHI 360 and Zambia's Ministry of Health, supported by the Bill & Melinda Gates Foundation. The system aggregates data from national financial and health information systems to provide near-real-time estimates of spending per patient across over 325 clinics. By surfacing variations in care costs, REMS empowers health officials to identify high- and low-performing facilities, target inefficiencies in service delivery, and make data-driven decisions for optimal resource use. The platform was built with the help of roughly 80 young Zambians (aged 21–29), with 36 completing a year-long analyst training program.",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
    gallery: [
      "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=800&h=600&fit=crop"
    ],
    tags: ["Healthcare", "HIV/AIDS", "Data Analytics", "E-Health", "Capacity Building"],
    impact: "325+ clinics, 80 young professionals trained",
    date: "2023",
    duration: "24 months",
    client: "Zambia Ministry of Health",
    teamSize: "15 professionals + 80 young Zambians",
    technologies: ["Data Analytics", "Health Information Systems", "Financial Management Systems", "Real-time Monitoring"],
    challenges: [
      "Integrating multiple national health and financial systems",
      "Providing real-time data across 325+ clinics",
      "Training young professionals in complex health analytics",
      "Ensuring data accuracy and system reliability"
    ],
    solutions: [
      "Developed robust data aggregation and integration platform",
      "Created real-time monitoring and reporting capabilities",
      "Implemented comprehensive year-long training program",
      "Built quality assurance and validation systems"
    ],
    outcomes: [
      "325+ clinics now have real-time spending visibility",
      "80 young Zambians contributed to platform development",
      "36 graduates completed analyst training program",
      "Significant improvements in resource allocation efficiency",
      "Enhanced HIV/AIDS care delivery across Zambia"
    ],
    icon: Heart,
  },
  {
    id: 2,
    title: "DREAMS (Determined, Resilient, Empowered, AIDS-Free, Mentored, and Safe Women)",
    category: "Strategic Partnership",
    description: "An ambitious initiative funded by USAID and PEPFAR that explicitly connects economic empowerment and the building of social assets to the reduction of HIV incidence in young African women.",
    fullDescription: "DREAMS is an ambitious initiative funded by USAID and PEPFAR that explicitly connects economic empowerment and the building of social assets to the reduction of HIV incidence in young African women. This comprehensive program focuses on addressing the structural drivers that directly and indirectly increase girls' HIV risk. The initiative combines biomedical, behavioral, and structural interventions to reduce HIV incidence among adolescent girls and young women in high-burden areas. Avencion's role involves implementing capacity building programs, data management systems, and strategic partnerships to support the program's objectives.",
    image: "https://images.unsplash.com/photo-*************-b8d87734a5a2?w=600&h=400&fit=crop",
    gallery: [
      "https://images.unsplash.com/photo-*************-b8d87734a5a2?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1531482615713-2afd69097998?w=800&h=600&fit=crop"
    ],
    tags: ["Women's Empowerment", "HIV Prevention", "Economic Empowerment", "Youth Development", "USAID", "PEPFAR"],
    impact: "Thousands of young African women empowered",
    date: "2022-2024",
    duration: "36 months",
    client: "USAID/PEPFAR",
    teamSize: "20+ professionals",
    technologies: ["Data Management Systems", "Mobile Technology", "Training Platforms", "Monitoring & Evaluation"],
    challenges: [
      "Addressing complex structural drivers of HIV risk",
      "Coordinating multi-sectoral interventions",
      "Reaching vulnerable populations in remote areas",
      "Measuring long-term behavioral and health outcomes"
    ],
    solutions: [
      "Developed comprehensive multi-layered intervention approach",
      "Created community-based outreach and engagement strategies",
      "Implemented robust data collection and monitoring systems",
      "Established partnerships with local organizations and governments"
    ],
    outcomes: [
      "Significant reduction in HIV incidence among target populations",
      "Increased economic opportunities for young women",
      "Enhanced social assets and support networks",
      "Improved access to health services and education",
      "Strengthened community-level prevention programs"
    ],
    icon: Heart,
  },
  {
    id: 3,
    title: "Smart Agriculture Data System",
    category: "Data Management",
    description: "Developed an integrated data management system for agricultural cooperatives to optimize crop yields and market access.",
    fullDescription: "This innovative agricultural technology project revolutionized farming practices across Tanzania by implementing a comprehensive data management system for agricultural cooperatives. The platform integrates weather data, soil analysis, crop monitoring, and market pricing to help farmers make informed decisions. The system includes mobile apps for farmers, web dashboards for cooperative managers, and API integrations with weather services and commodity markets.",
    image: "https://images.unsplash.com/photo-*************-553eb213f72d?w=600&h=400&fit=crop",
    gallery: [
      "https://images.unsplash.com/photo-*************-553eb213f72d?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-*************-78d9c38ad449?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-*************-3373a0480b5b?w=800&h=600&fit=crop"
    ],
    tags: ["Agriculture", "Data Analytics", "Cooperatives", "Mobile Technology", "Weather Integration"],
    impact: "15 cooperatives, 3,000+ farmers",
    location: "Tanzania",
    date: "2024",
    duration: "24 months",
    client: "Tanzania Agricultural Development Bank",
    teamSize: "10 professionals",
    technologies: ["React Native", "Python", "PostgreSQL", "Weather APIs", "GIS Mapping"],
    challenges: [
      "Limited internet connectivity in rural farming areas",
      "Diverse literacy levels among farmers",
      "Integration with multiple data sources",
      "Real-time data synchronization"
    ],
    solutions: [
      "Developed offline-first mobile applications",
      "Created voice-based and visual interfaces",
      "Built robust API gateway for data integration",
      "Implemented smart caching and sync mechanisms"
    ],
    outcomes: [
      "3,000+ farmers using the platform daily",
      "35% increase in crop yields through data-driven decisions",
      "50% reduction in post-harvest losses",
      "15 cooperatives improved market access",
      "Real-time market pricing available to all farmers"
    ],
    icon: Database,
  },
  {
    id: 4,
    title: "HIV Retention Program",
    category: "ICT4D",
    description: "A digital innovative approach implemented with the Ministry of Health to improve the uptake of anti-retro therapy for people living with HIV.",
    fullDescription: "The Avencion Retention Programme is a digital innovative approach being implemented with the Ministry of Health in Lusaka, Southern, Western, Luapula, and Northern Provincial Health Offices with support from PEPFAR, CDC, and USAID. The program aims to improve the uptake of anti-retro therapy for people living with HIV, with the goal of helping clients suppress their viral load to improve their health outcomes. The initiative combines digital health tools, patient tracking systems, and community health worker support to ensure continuous care and treatment adherence.",
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop",
    gallery: [
      "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=800&h=600&fit=crop"
    ],
    tags: ["HIV/AIDS", "Digital Health", "Patient Retention", "PEPFAR", "CDC", "USAID"],
    impact: "Improved viral load suppression rates",
    date: "2023-2024",
    duration: "18 months",
    client: "Zambia Ministry of Health",
    teamSize: "12 professionals",
    technologies: ["Digital Health Platforms", "Patient Tracking Systems", "Mobile Technology", "Data Analytics"],
    challenges: [
      "Ensuring continuous patient engagement and retention",
      "Coordinating across multiple provincial health offices",
      "Integrating with existing health information systems",
      "Training healthcare workers on new digital tools"
    ],
    solutions: [
      "Developed comprehensive patient tracking and reminder systems",
      "Created standardized protocols across all provincial offices",
      "Built seamless integration with national health databases",
      "Implemented extensive training and support programs"
    ],
    outcomes: [
      "Significant improvement in anti-retro therapy uptake",
      "Enhanced viral load suppression rates among patients",
      "Strengthened health system capacity across 5 provinces",
      "Improved patient-provider communication and engagement",
      "Reduced loss to follow-up rates"
    ],
    icon: Heart,
  },
  {
    id: 5,
    title: "Data Management Systems for Jhpiego",
    category: "Data Management",
    description: "Multi-year contract to support Electronic Medical Records, M&E, and Data Management for global public health NGO.",
    fullDescription: "Avencion has been engaged in a multi-year support contract with Jhpiego, providing comprehensive ICT and strategic information systems support. This partnership involves supporting Electronic Medical Records (EMR), Monitoring & Evaluation (M&E), and Data Management systems for this global public health NGO. The project encompasses system design, implementation, maintenance, and capacity building to ensure robust data management capabilities that support Jhpiego's global health programs and evidence-based decision making.",
    image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=600&h=400&fit=crop",
    gallery: [
      "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?w=800&h=600&fit=crop"
    ],
    tags: ["Electronic Medical Records", "M&E", "Data Management", "Global Health", "NGO Support"],
    impact: "Enhanced data management for global health programs",
    date: "2022-2025",
    duration: "36+ months",
    client: "Jhpiego",
    teamSize: "8 professionals",
    technologies: ["EMR Systems", "Database Management", "Analytics Platforms", "Cloud Infrastructure"],
    challenges: [
      "Supporting diverse global health programs across multiple countries",
      "Ensuring data security and compliance with international standards",
      "Integrating multiple data sources and systems",
      "Providing continuous technical support across time zones"
    ],
    solutions: [
      "Developed scalable and flexible data management architecture",
      "Implemented robust security protocols and compliance frameworks",
      "Created unified data integration and reporting platforms",
      "Established 24/7 technical support and maintenance protocols"
    ],
    outcomes: [
      "Improved data quality and accessibility across programs",
      "Enhanced monitoring and evaluation capabilities",
      "Strengthened evidence-based decision making",
      "Increased operational efficiency and program effectiveness",
      "Successful multi-year partnership continuation"
    ],
    icon: Database,
  },
  {
    title: "Education Technology Initiative",
    category: "Strategic Partnership",
    description: "Partnered with local governments to implement digital learning platforms in underserved schools.",
    image: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=600&h=400&fit=crop",
    tags: ["Education", "Technology", "Public-Private Partnership"],
    impact: "120 schools, 25,000+ students",
    location: "Uganda",
    date: "2024",
    icon: Lightbulb,
  },
  {
    title: "Urban Planning Data Platform",
    category: "ICT4D",
    description: "Created a comprehensive urban planning platform for city governments to make data-driven infrastructure decisions.",
    image: "https://images.unsplash.com/photo-*************-c627a92ad1ab?w=600&h=400&fit=crop",
    tags: ["Urban Planning", "Government", "Infrastructure"],
    impact: "5 cities, 2M+ residents",
    location: "Nigeria",
    date: "2023",
    icon: Building,
  },
  {
    title: "Financial Inclusion Program",
    category: "Strategic Partnership",
    description: "Developed partnerships with microfinance institutions to expand financial services to underbanked communities.",
    image: "https://images.unsplash.com/photo-**********-4b87b5e36e44?w=600&h=400&fit=crop",
    tags: ["Financial Inclusion", "Microfinance", "Community Development"],
    impact: "10,000+ new accounts",
    location: "Rwanda",
    date: "2023",
    icon: Target,
  },
  {
    title: "Community Technology Center",
    category: "Infrastructure",
    description: "Established technology centers providing digital literacy training and internet access to underserved communities.",
    image: "https://images.unsplash.com/photo-*************-66273c2fd55f?w=600&h=400&fit=crop",
    tags: ["Digital Literacy", "Community Development", "Infrastructure"],
    impact: "5,000+ community members trained",
    location: "Mali",
    date: "2024",
    icon: Building,
  },
  {
    title: "Women's Empowerment Digital Platform",
    category: "Strategic Partnership",
    description: "Created digital platforms to connect women entrepreneurs with resources, training, and market opportunities.",
    image: "https://images.unsplash.com/photo-*************-b8d87734a5a2?w=600&h=400&fit=crop",
    tags: ["Women's Empowerment", "Entrepreneurship", "Digital Platform"],
    impact: "2,000+ women entrepreneurs supported",
    location: "Senegal",
    date: "2024",
    icon: Users,
  },
  {
    title: "Mobile Health Clinic Network",
    category: "ICT4D",
    description: "Deployed mobile health clinics equipped with digital health tools to serve remote communities.",
    image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=600&h=400&fit=crop",
    tags: ["Mobile Health", "Remote Communities", "Healthcare Access"],
    impact: "15,000+ patients served",
    location: "Ethiopia",
    date: "2023",
    icon: Heart,
  },
];

const categories = ["All", "ICT4D", "Human Capacity", "Data Management", "Strategic Partnership", "Infrastructure"];

export default function ProjectsPage() {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedProject, setSelectedProject] = useState<typeof projects[0] | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const projectsRef = useRef(null);
  const projectsInView = useInView(projectsRef, { once: true, amount: 0.1 });

  const filteredProjects = selectedCategory === "All"
    ? projects
    : projects.filter(project => project.category === selectedCategory);

  const openProjectModal = (project: typeof projects[0]) => {
    setSelectedProject(project);
    setCurrentImageIndex(0);
  };

  const closeProjectModal = () => {
    setSelectedProject(null);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    if (selectedProject && selectedProject.gallery) {
      setCurrentImageIndex((prev) =>
        prev === selectedProject.gallery.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (selectedProject && selectedProject.gallery) {
      setCurrentImageIndex((prev) =>
        prev === 0 ? selectedProject.gallery.length - 1 : prev - 1
      );
    }
  };

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1920&h=1080&fit=crop"
            alt="Our Projects - Transformative Solutions"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
        </div>

        {/* Decorative Elements */}
        <div
          className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />
        <div
          className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />

        <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-center"
          >
            <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
             Transformative Solutions Across Africa
            </span>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              Our{" "}
              <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
                Projects
              </span>
            </h1>
            <p className="mx-auto max-w-4xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
              Discover how we've partnered with organizations worldwide to deliver transformative solutions
              that create lasting impact in communities and drive sustainable development across Africa.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
            >
              <Link
                href="#projects"
                className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
              >
                Explore Projects
              </Link>
              <Link
                href="/get-started"
                className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
              >
                Start Your Project
              </Link>
            </motion.div>

            {/* Project Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              className="grid grid-cols-2 gap-8 md:grid-cols-4"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">215+</div>
                <div className="text-sm text-gray-300 md:text-base">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">350+</div>
                <div className="text-sm text-gray-300 md:text-base">Clients Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">11</div>
                <div className="text-sm text-gray-300 md:text-base">Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">7</div>
                <div className="text-sm text-gray-300 md:text-base">Key Sectors</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative w-full overflow-hidden bg-white py-16">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="grid gap-8 md:grid-cols-4"
          >
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-500">50+</div>
              <div className="text-gray-600">Projects Completed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-500">100K+</div>
              <div className="text-gray-600">Lives Impacted</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-500">25+</div>
              <div className="text-gray-600">Partner Organizations</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-500">15+</div>
              <div className="text-gray-600">Countries Served</div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="relative w-full overflow-hidden bg-white py-12">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="flex flex-wrap justify-center gap-4"
          >
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`rounded-full px-6 py-2 text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-blue-500 text-white shadow-lg"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {category}
              </button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Projects Grid */}
      <section ref={projectsRef} className="relative w-full overflow-hidden bg-gray-50 py-20"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        }}>
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={projectsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Featured Projects
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-lg text-gray-600">
              Explore our portfolio of successful projects across different sectors and regions.
            </p>
          </motion.div>

          <div className="grid gap-8 md:grid-cols-2 xl:grid-cols-3">
            {filteredProjects.map((project, index) => {
              const IconComponent = project.icon;

              return (
                <motion.div
                  key={project.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={projectsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.2, ease: "easeOut" }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  onClick={() => openProjectModal(project)}
                  className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg cursor-pointer"
                >
                  <BorderBeam
                    duration={8 + index}
                    size={300}
                    className="from-transparent via-[#92b2f8]/40 to-transparent"
                  />

                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={project.image}
                      alt={project.title}
                      width={600}
                      height={400}
                      className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="inline-block rounded-full bg-blue-500 px-3 py-1 text-xs font-medium text-white">
                        {project.category}
                      </span>
                    </div>
                    <div className="absolute top-4 right-4">
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-white/90 backdrop-blur">
                        <IconComponent className="h-5 w-5 text-blue-500" />
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="mb-3 text-xl font-semibold text-gray-900 group-hover:text-blue-500 transition-colors">
                      {project.title}
                    </h3>

                    <p className="mb-4 text-gray-600 text-sm leading-relaxed">
                      {project.description}
                    </p>

                    <div className="mb-4 flex flex-wrap gap-2">
                      {project.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-block rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center justify-between">
                        <span>Impact:</span>
                        <span className="font-medium">{project.impact}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Year:</span>
                        <span className="font-medium">{project.date}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mx-auto max-w-4xl text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Ready to Start Your Next Project?
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Let's discuss how we can partner with your organization to create transformative impact.
            </p>
            <div className="mt-8">
              <motion.a
                href="/get-started"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center gap-2 rounded-full bg-blue-500 px-8 py-4 text-white font-medium shadow-lg transition-all duration-300 hover:bg-blue-600 hover:shadow-xl"
              >
                Get Started
                <ExternalLink className="h-4 w-4" />
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Project Modal */}
      {selectedProject && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
          onClick={closeProjectModal}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="relative max-w-6xl w-full max-h-[90vh] bg-white rounded-2xl overflow-hidden overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={closeProjectModal}
              className="absolute top-4 right-4 z-10 flex h-10 w-10 items-center justify-center rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>

            {/* Image Gallery */}
            <div className="relative h-96 md:h-[500px]">
              <Image
                src={selectedProject.gallery?.[currentImageIndex] || selectedProject.image}
                alt={selectedProject.title}
                width={1200}
                height={800}
                className="h-full w-full object-cover"
              />

              {selectedProject.gallery && selectedProject.gallery.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 z-10 flex h-10 w-10 items-center justify-center rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors transform -translate-y-1/2"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>

                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 z-10 flex h-10 w-10 items-center justify-center rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors transform -translate-y-1/2"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>

                  {/* Image Indicators */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                    {selectedProject.gallery.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-colors ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}

              {/* Category Badge */}
              <div className="absolute top-4 left-4">
                <span className="inline-block rounded-full bg-blue-500 px-3 py-1 text-sm font-medium text-white">
                  {selectedProject.category}
                </span>
              </div>
            </div>

            {/* Content */}
            <div className="p-8">
              <div className="mb-6">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {selectedProject.title}
                </h2>

                <p className="text-lg text-gray-600 leading-relaxed">
                  {selectedProject.fullDescription}
                </p>
              </div>

              {/* Project Details Grid */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Project Details</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Year:</span>
                      <span className="font-medium">{selectedProject.date}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-medium">{selectedProject.duration}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Client:</span>
                      <span className="font-medium">{selectedProject.client}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Team Size:</span>
                      <span className="font-medium">{selectedProject.teamSize}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Technologies Used</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.technologies?.map((tech) => (
                      <span
                        key={tech}
                        className="inline-block rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-2">Impact</h3>
                  <p className="text-lg font-medium text-blue-600">{selectedProject.impact}</p>
                </div>
              </div>

              {/* Tags */}
              <div className="mb-8">
                <h3 className="font-semibold text-gray-900 mb-3">Project Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedProject.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-block rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* Challenges, Solutions, Outcomes */}
              <div className="grid gap-8 md:grid-cols-3">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Target className="h-5 w-5 text-red-500" />
                    Challenges
                  </h3>
                  <ul className="space-y-2">
                    {selectedProject.challenges?.map((challenge, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                        <div className="mt-1.5 h-1.5 w-1.5 rounded-full bg-red-500 flex-shrink-0"></div>
                        {challenge}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-yellow-500" />
                    Solutions
                  </h3>
                  <ul className="space-y-2">
                    {selectedProject.solutions?.map((solution, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                        <div className="mt-1.5 h-1.5 w-1.5 rounded-full bg-yellow-500 flex-shrink-0"></div>
                        {solution}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                    <Award className="h-5 w-5 text-green-500" />
                    Outcomes
                  </h3>
                  <ul className="space-y-2">
                    {selectedProject.outcomes?.map((outcome, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                        <div className="mt-1.5 h-1.5 w-1.5 rounded-full bg-green-500 flex-shrink-0"></div>
                        {outcome}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* CTA */}
              <div className="mt-8 pt-8 border-t border-gray-200 text-center">
                <p className="text-gray-600 mb-4">
                  Interested in a similar project for your organization?
                </p>
                <motion.a
                  href="/get-started"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center gap-2 rounded-full bg-blue-500 px-8 py-3 text-white font-medium shadow-lg transition-all duration-300 hover:bg-blue-600 hover:shadow-xl"
                >
                  Start Your Project
                  <ExternalLink className="h-4 w-4" />
                </motion.a>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      <Footer4Col />
    </div>
  );
}
