"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import { Spotlight } from "@/components/ui/spotlight";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  MapPin,
  Clock,
  Users,
  Heart,
  Lightbulb,
  Target,
  Award,
  ExternalLink,
  Briefcase,
  GraduationCap,
} from "lucide-react";

const jobOpenings = [
  {
    title: "Senior ICT4D Specialist",
    department: "Technology",
    location: "Remote / Nairobi, Kenya",
    type: "Full-time",
    experience: "5+ years",
    description: "Lead the design and implementation of ICT4D solutions for development projects across Africa.",
    requirements: [
      "Master's degree in Computer Science, Information Systems, or related field",
      "5+ years experience in ICT4D project implementation",
      "Strong knowledge of development frameworks and methodologies",
      "Experience with data management systems and analytics",
    ],
    responsibilities: [
      "Design and implement technology solutions for development projects",
      "Lead technical teams and coordinate with stakeholders",
      "Conduct capacity building workshops and training sessions",
      "Monitor and evaluate project outcomes and impact",
    ],
  },
  {
    title: "Capacity Building Manager",
    department: "Human Development",
    location: "Accra, Ghana",
    type: "Full-time",
    experience: "4+ years",
    description: "Develop and deliver comprehensive capacity building programs for partner organizations.",
    requirements: [
      "Bachelor's degree in Education, Development Studies, or related field",
      "4+ years experience in training and capacity development",
      "Excellent facilitation and communication skills",
      "Experience working with NGOs and development organizations",
    ],
    responsibilities: [
      "Design and implement capacity building curricula",
      "Facilitate training workshops and seminars",
      "Assess training needs and develop customized programs",
      "Monitor and evaluate training effectiveness",
    ],
  },
  {
    title: "Data Analyst",
    department: "Data & Analytics",
    location: "Remote / Lagos, Nigeria",
    type: "Full-time",
    experience: "3+ years",
    description: "Analyze project data to generate insights and support evidence-based decision making.",
    requirements: [
      "Bachelor's degree in Statistics, Mathematics, or related field",
      "3+ years experience in data analysis and visualization",
      "Proficiency in R, Python, or similar analytical tools",
      "Experience with database management and SQL",
    ],
    responsibilities: [
      "Collect, clean, and analyze project data",
      "Create data visualizations and reports",
      "Support monitoring and evaluation activities",
      "Provide data-driven recommendations to project teams",
    ],
  },
  {
    title: "Partnership Development Officer",
    department: "Strategic Partnerships",
    location: "Kampala, Uganda",
    type: "Full-time",
    experience: "3+ years",
    description: "Identify and develop strategic partnerships to expand our impact and reach.",
    requirements: [
      "Bachelor's degree in Business, International Relations, or related field",
      "3+ years experience in partnership development or business development",
      "Strong networking and relationship building skills",
      "Knowledge of the development sector and funding landscape",
    ],
    responsibilities: [
      "Identify potential partnership opportunities",
      "Develop partnership proposals and agreements",
      "Maintain relationships with existing partners",
      "Support fundraising and resource mobilization efforts",
    ],
  },
];

const benefits = [
  {
    title: "Competitive Compensation",
    description: "We offer competitive salaries and performance-based bonuses.",
    icon: Award,
  },
  {
    title: "Professional Development",
    description: "Continuous learning opportunities and career advancement support.",
    icon: GraduationCap,
  },
  {
    title: "Flexible Work",
    description: "Remote work options and flexible scheduling to support work-life balance.",
    icon: Clock,
  },
  {
    title: "Health & Wellness",
    description: "Comprehensive health insurance and wellness programs.",
    icon: Heart,
  },
  {
    title: "Impact-Driven Work",
    description: "Meaningful work that creates positive change in communities worldwide.",
    icon: Target,
  },
  {
    title: "Collaborative Culture",
    description: "Work with passionate professionals in a supportive team environment.",
    icon: Users,
  },
];

export default function CareersPage() {
  const jobsRef = useRef(null);
  const benefitsRef = useRef(null);
  
  const jobsInView = useInView(jobsRef, { once: true, amount: 0.1 });
  const benefitsInView = useInView(benefitsRef, { once: true, amount: 0.3 });

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-**********142-b8d87734a5a2?w=1920&h=1080&fit=crop"
            alt="Careers - Join Our Mission"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
        </div>

        {/* Decorative Elements */}
        <div
          className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />
        <div
          className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />

        <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-center"
          >
            <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
             Join Our Team - Make an Impact
            </span>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              Join Our{" "}
              <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
                Mission
              </span>
            </h1>
            <p className="mx-auto max-w-4xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
              Be part of a team that's creating transformative impact across communities in Africa.
              We're looking for passionate professionals who share our vision for inclusive growth and sustainable development.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
            >
              <Link
                href="#openings"
                className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
              >
                View Open Positions
              </Link>
              <Link
                href="/get-started"
                className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
              >
                Contact Us
              </Link>
            </motion.div>

            {/* Career Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              className="grid grid-cols-2 gap-8 md:grid-cols-4"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">50+</div>
                <div className="text-sm text-gray-300 md:text-base">Team Members</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">11</div>
                <div className="text-sm text-gray-300 md:text-base">Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">15+</div>
                <div className="text-sm text-gray-300 md:text-base">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">100%</div>
                <div className="text-sm text-gray-300 md:text-base">Remote Friendly</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Why Work With Us */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Why Work With Us?
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-lg text-gray-600">
              Join a purpose-driven organization where your skills make a real difference.
            </p>
          </motion.div>

          <div ref={benefitsRef} className="grid gap-8 md:grid-cols-2 xl:grid-cols-3">
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              
              return (
                <motion.div
                  key={benefit.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={benefitsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.2, ease: "easeOut" }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-8 shadow-lg"
                >
                  <div className="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                    <IconComponent className="h-6 w-6 text-[#92b2f8]" />
                  </div>
                  <h3 className="mb-2 text-xl font-semibold text-gray-900">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">
                    {benefit.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Job Openings */}
      <section id="openings" ref={jobsRef} className="relative w-full overflow-hidden bg-gray-50 py-20"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        }}>
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={jobsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Current Openings
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-lg text-gray-600">
              Explore exciting opportunities to grow your career while making a positive impact.
            </p>
          </motion.div>

          <div className="space-y-8">
            {jobOpenings.map((job, index) => (
              <motion.div
                key={job.title}
                initial={{ opacity: 0, y: 30 }}
                animate={jobsInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.6, delay: index * 0.1 + 0.2, ease: "easeOut" }}
                whileHover={{ y: -5 }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg"
              >
                <BorderBeam
                  duration={8 + index}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                />
                
                <div className="p-8">
                  <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                      <h3 className="text-2xl font-semibold text-gray-900 group-hover:text-blue-500 transition-colors">
                        {job.title}
                      </h3>
                      <p className="text-blue-500 font-medium">{job.department}</p>
                    </div>
                    
                    <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {job.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {job.type}
                      </div>
                      <div className="flex items-center gap-1">
                        <Briefcase className="h-4 w-4" />
                        {job.experience}
                      </div>
                    </div>
                  </div>
                  
                  <p className="mb-6 text-gray-600 leading-relaxed">
                    {job.description}
                  </p>
                  
                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <h4 className="mb-3 font-semibold text-gray-900">Requirements</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        {job.requirements.map((req, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="mt-1.5 h-1.5 w-1.5 rounded-full bg-blue-500 flex-shrink-0"></div>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="mb-3 font-semibold text-gray-900">Responsibilities</h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        {job.responsibilities.map((resp, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="mt-1.5 h-1.5 w-1.5 rounded-full bg-blue-500 flex-shrink-0"></div>
                            {resp}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="mt-8 flex justify-end">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="inline-flex items-center gap-2 rounded-full bg-blue-500 px-6 py-3 text-white font-medium shadow-lg transition-all duration-300 hover:bg-blue-600 hover:shadow-xl"
                    >
                      Apply Now
                      <ExternalLink className="h-4 w-4" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mx-auto max-w-4xl text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Don't See the Right Role?
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              We're always looking for talented individuals who share our passion for creating positive change. 
              Send us your resume and let us know how you'd like to contribute.
            </p>
            <div className="mt-8">
              <motion.a
                href="/get-started"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center gap-2 rounded-full bg-blue-500 px-8 py-4 text-white font-medium shadow-lg transition-all duration-300 hover:bg-blue-600 hover:shadow-xl"
              >
                Get in Touch
                <ExternalLink className="h-4 w-4" />
              </motion.a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
