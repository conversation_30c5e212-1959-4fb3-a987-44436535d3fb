"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import Timeline from "@/components/mvpblocks/timeline";
import { Spotlight } from "@/components/ui/spotlight";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  Globe,
  Users,
  Heart,
  Lightbulb,
  Sparkles,
  Rocket,
  Target,
  Award,
  Building,
  Calendar,
} from "lucide-react";





export default function AboutPage() {

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1552664730-d307ca884978?w=1920&h=1080&fit=crop"
            alt="About Avencion Limited - Our Story"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
        </div>

        {/* Decorative Elements */}
        <div
          className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />
        <div
          className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />

        <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-center"
          >
            <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
             Our Story - Since 2010
            </span>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              About{" "}
              <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
                Avencion Limited
              </span>
            </h1>
            <p className="mx-auto max-w-4xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
              Avencion Group was founded in 2010 as a result of the merger of an international development consulting firm and technology company.
              Our team has experience in diverse markets across 11 African countries.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
            >
              <Link
                href="/projects"
                className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
              >
                View Our Projects
              </Link>
              <Link
                href="/get-started"
                className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
              >
                Work With Us
              </Link>
            </motion.div>

            {/* Countries Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              className="grid grid-cols-2 gap-8 md:grid-cols-4"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">11</div>
                <div className="text-sm text-gray-300 md:text-base">African Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">7</div>
                <div className="text-sm text-gray-300 md:text-base">Key Sectors</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">350+</div>
                <div className="text-sm text-gray-300 md:text-base">Trusted Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">2010</div>
                <div className="text-sm text-gray-300 md:text-base">Founded</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <div className="mx-auto mb-24 max-w-7xl">
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="relative z-10 grid gap-12 md:grid-cols-2"
            >
              <motion.div
                whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-10 shadow-lg"
              >
                <BorderBeam
                  duration={8}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                />

                <div className="mb-6 inline-flex aspect-square h-16 w-16 flex-1 items-center justify-center rounded-2xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                  <Rocket className="h-8 w-8 text-[#92b2f8]" />
                </div>

                <div className="space-y-4">
                  <h2 className="mb-4 text-3xl font-bold text-gray-900">
                    Our Mission
                  </h2>

                  <p className="text-lg leading-relaxed text-gray-600">
                    We work across a variety of sectors including health, sustainable energy, technology & telecommunications,
                    infrastructure, agriculture, education and government. We design and implement high impact solutions for
                    governments, businesses, and communities.
                  </p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-10 shadow-lg"
              >
                <BorderBeam
                  duration={8}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                  reverse
                />
                <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                  <Target className="h-8 w-8 text-[#92b2f8]" />
                </div>

                <h2 className="mb-4 text-3xl font-bold text-gray-900">
                  Our Partners
                </h2>

                <p className="text-lg leading-relaxed text-gray-600">
                  We have developed strong partnerships and designed programs with universities, research institutions,
                  government agencies, international NGOs and community-based organizations. We align with clients and
                  stakeholders to devise and implement national performance enhancement programs.
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <Timeline />

      {/* Our Approach Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mx-auto max-w-4xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-6">
              Our Approach
            </h2>
            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              We align with clients and stakeholders to devise and implement national performance enhancement programs,
              increase technical capacity in government and private sector institutions, and create information technology
              platforms to increase efficiency and productivity of organizations.
            </p>
            <p className="text-lg text-gray-600 leading-relaxed">
              Avencion believes achieving desired outcomes requires aligning public & private sector goals, providing
              transformative leadership for programs developing infrastructure, and increasing human capacity to unlock value.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl p-8 md:p-12">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Our Reputation in Africa
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed">
                Our work in Africa has earned us a reputation for constructing sustainable relationships across sectors
                through inclusive communication, commitment, and integrity. Our robust network of partners and past
                experiences distinguish us from other firms. Avencion's diverse team members operate as architects of
                collaboration and specialists in the interface between public and private sectors, delivering solutions
                for some of the most challenging issues facing the world today.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
