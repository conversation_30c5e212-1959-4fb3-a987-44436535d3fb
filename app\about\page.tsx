"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import Timeline from "@/components/mvpblocks/timeline";
import { Spotlight } from "@/components/ui/spotlight";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  Globe,
  Users,
  Heart,
  Lightbulb,
  Sparkles,
  Rocket,
  Target,
  Award,
  Building,
  Calendar,
} from "lucide-react";

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Chief Executive Officer",
    image: "https://randomuser.me/api/portraits/women/44.jpg",
    bio: "15+ years of experience in organizational development and strategic partnerships.",
  },
  {
    name: "<PERSON>",
    role: "Chief Technology Officer",
    image: "https://randomuser.me/api/portraits/men/32.jpg",
    bio: "Expert in ICT4D implementation and data-driven management systems.",
  },
  {
    name: "Dr. <PERSON><PERSON>",
    role: "Head of Capacity Building",
    image: "https://randomuser.me/api/portraits/women/67.jpg",
    bio: "PhD in Development Studies with focus on human capacity development.",
  },
  {
    name: "<PERSON>",
    role: "Strategic Partnerships Director",
    image: "https://randomuser.me/api/portraits/men/55.jpg",
    bio: "Specialist in building transformative partnerships across sectors.",
  },
];



export default function AboutPage() {
  const teamRef = useRef(null);

  const teamInView = useInView(teamRef, { once: true, amount: 0.3 });

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative w-full overflow-hidden bg-gray-50 pb-20 pt-32"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        }}>
        <Spotlight
          gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, rgba(146, 178, 248, 0.2) 0, rgba(146, 178, 248, 0.1) 50%, rgba(146, 178, 248, 0) 80%)"
          gradientSecond="radial-gradient(50% 50% at 50% 50%, rgba(146, 178, 248, 0.2) 0, rgba(146, 178, 248, 0.1) 80%, transparent 100%)"
          gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(332, 100%, 85%, 0.06) 0, hsla(327, 100%, 85%, 0.06) 80%, transparent 100%)"
        />

        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="mx-auto mb-16 max-w-4xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
              About <span className="text-blue-500">Avencion Limited</span>
            </h1>
            <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
              We are a forward-thinking organization dedicated to empowering businesses and institutions 
              through strategic partnerships, innovative ICT4D solutions, and comprehensive capacity building programs.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <div className="mx-auto mb-24 max-w-7xl">
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="relative z-10 grid gap-12 md:grid-cols-2"
            >
              <motion.div
                whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-10 shadow-lg"
              >
                <BorderBeam
                  duration={8}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                />

                <div className="mb-6 inline-flex aspect-square h-16 w-16 flex-1 items-center justify-center rounded-2xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                  <Rocket className="h-8 w-8 text-[#92b2f8]" />
                </div>

                <div className="space-y-4">
                  <h2 className="mb-4 text-3xl font-bold text-gray-900">
                    Our Mission
                  </h2>

                  <p className="text-lg leading-relaxed text-gray-600">
                    To deliver impactful solutions by prioritizing clients, leveraging human capacity, 
                    and fostering growth through sustainable partnerships that create lasting positive change.
                  </p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-10 shadow-lg"
              >
                <BorderBeam
                  duration={8}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                  reverse
                />
                <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                  <Target className="h-8 w-8 text-[#92b2f8]" />
                </div>

                <h2 className="mb-4 text-3xl font-bold text-gray-900">
                  Our Vision
                </h2>

                <p className="text-lg leading-relaxed text-gray-600">
                  To be the leading catalyst for transformative growth, empowering organizations 
                  worldwide through innovative partnerships and inclusive solutions that drive sustainable development.
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <Timeline />

      {/* Team Section */}
      <section ref={teamRef} className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={teamInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-12 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Meet Our Team
            </h2>
            <p className="mx-auto mt-4 max-w-2xl text-lg text-gray-600">
              The passionate professionals driving our mission forward.
            </p>
          </motion.div>

          <div className="grid gap-8 md:grid-cols-2 xl:grid-cols-4">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 30 }}
                animate={teamInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.6, delay: index * 0.1 + 0.2, ease: "easeOut" }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-6 shadow-lg text-center"
              >
                <div className="mb-4">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="mx-auto h-24 w-24 rounded-full object-cover ring-4 ring-blue-500/20"
                  />
                </div>
                <h3 className="mb-2 text-xl font-semibold text-gray-900">
                  {member.name}
                </h3>
                <p className="mb-3 text-blue-500 font-medium">
                  {member.role}
                </p>
                <p className="text-sm text-gray-600">
                  {member.bio}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
