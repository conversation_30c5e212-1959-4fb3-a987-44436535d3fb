"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import Timeline from "@/components/mvpblocks/timeline";
import { Spotlight } from "@/components/ui/spotlight";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  Globe,
  Users,
  Heart,
  Lightbulb,
  Sparkles,
  Rocket,
  Target,
  Award,
  Building,
  Calendar,
} from "lucide-react";





export default function AboutPage() {

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative w-full overflow-hidden bg-gray-50 pb-20 pt-32"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        }}>
        <Spotlight
          gradientFirst="radial-gradient(68.54% 68.72% at 55.02% 31.46%, rgba(146, 178, 248, 0.2) 0, rgba(146, 178, 248, 0.1) 50%, rgba(146, 178, 248, 0) 80%)"
          gradientSecond="radial-gradient(50% 50% at 50% 50%, rgba(146, 178, 248, 0.2) 0, rgba(146, 178, 248, 0.1) 80%, transparent 100%)"
          gradientThird="radial-gradient(50% 50% at 50% 50%, hsla(332, 100%, 85%, 0.06) 0, hsla(327, 100%, 85%, 0.06) 80%, transparent 100%)"
        />

        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="mx-auto mb-16 max-w-4xl text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
              About <span className="text-blue-500">Avencion Limited</span>
            </h1>
            <p className="mt-6 text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Avencion Group was founded in 2010 as a result of the merger of an international development consulting firm and technology company.
              Our team has experience in diverse markets including Tanzania, Mozambique, Burundi, South Africa, Zambia, Zimbabwe, Uganda, Malawi, Kenya, Ghana and Rwanda.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <div className="mx-auto mb-24 max-w-7xl">
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="relative z-10 grid gap-12 md:grid-cols-2"
            >
              <motion.div
                whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-10 shadow-lg"
              >
                <BorderBeam
                  duration={8}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                />

                <div className="mb-6 inline-flex aspect-square h-16 w-16 flex-1 items-center justify-center rounded-2xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                  <Rocket className="h-8 w-8 text-[#92b2f8]" />
                </div>

                <div className="space-y-4">
                  <h2 className="mb-4 text-3xl font-bold text-gray-900">
                    Our Mission
                  </h2>

                  <p className="text-lg leading-relaxed text-gray-600">
                    We work across a variety of sectors including health, sustainable energy, technology & telecommunications,
                    infrastructure, agriculture, education and government. We design and implement high impact solutions for
                    governments, businesses, and communities.
                  </p>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ y: -5, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                className="group relative block overflow-hidden rounded-2xl border border-gray-200 bg-white p-10 shadow-lg"
              >
                <BorderBeam
                  duration={8}
                  size={300}
                  className="from-transparent via-[#92b2f8]/40 to-transparent"
                  reverse
                />
                <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-[#92b2f8]/20 to-[#92b2f8]/5">
                  <Target className="h-8 w-8 text-[#92b2f8]" />
                </div>

                <h2 className="mb-4 text-3xl font-bold text-gray-900">
                  Our Partners
                </h2>

                <p className="text-lg leading-relaxed text-gray-600">
                  We have developed strong partnerships and designed programs with universities, research institutions,
                  government agencies, international NGOs and community-based organizations. We align with clients and
                  stakeholders to devise and implement national performance enhancement programs.
                </p>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <Timeline />

      {/* Our Approach Section */}
      <section className="relative w-full overflow-hidden bg-white py-20">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mx-auto max-w-4xl text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-6">
              Our Approach
            </h2>
            <p className="text-lg text-gray-600 leading-relaxed mb-8">
              We align with clients and stakeholders to devise and implement national performance enhancement programs,
              increase technical capacity in government and private sector institutions, and create information technology
              platforms to increase efficiency and productivity of organizations.
            </p>
            <p className="text-lg text-gray-600 leading-relaxed">
              Avencion believes achieving desired outcomes requires aligning public & private sector goals, providing
              transformative leadership for programs developing infrastructure, and increasing human capacity to unlock value.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-2xl p-8 md:p-12">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Our Reputation in Africa
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed">
                Our work in Africa has earned us a reputation for constructing sustainable relationships across sectors
                through inclusive communication, commitment, and integrity. Our robust network of partners and past
                experiences distinguish us from other firms. Avencion's diverse team members operate as architects of
                collaboration and specialists in the interface between public and private sectors, delivering solutions
                for some of the most challenging issues facing the world today.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
