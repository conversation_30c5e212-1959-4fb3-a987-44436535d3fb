"use client";

import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Navbar from "@/components/mvpblocks/navbar";
import Footer4Col from "@/components/mvpblocks/footer-4col";
import { BorderBeam } from "@/components/ui/border-beam";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Users,
  Target,
  Clock,
  Globe,
  Award,
} from "lucide-react";

// DOT Project FAQs
const dotFAQs = [
  {
    id: 1,
    question: "What is the Going Beyond Beyond Project?",
    answer: "The Going Beyond – Partnering for a Youth-Led Future project is a partnership between DOT and the Mastercard Foundation that aims to scale DOT's evidence-based, peer-to-peer model and will be executed in four African countries – Côte d'Ivoire, Malawi, Zambia, and Tanzania. Over five years, and with the support of DOT regional hubs in Africa, the Going Beyond project will progressively transfer the responsibility for delivering DOT's programs and equip youth as leaders of the future.",
    category: "Project Overview"
  },
  {
    id: 2,
    question: "What are the project goals?",
    answer: "The project aims to scale DOT's evidence-based, peer-to-peer model across four African countries, progressively transfer program delivery responsibility to local youth, and equip young people as leaders of the future through comprehensive capacity building and mentorship programs.",
    category: "Project Overview"
  },
  {
    id: 3,
    question: "What are the expected outcomes and impacts of the project on youth-led entrepreneurship and economic development?",
    answer: "The project expects to significantly boost youth-led entrepreneurship by providing direct support to young MSME owners, creating sustainable economic opportunities, and building local capacity for ongoing program delivery. The impact includes increased youth employment, enhanced entrepreneurial skills, and strengthened local economies across the four target countries.",
    category: "Impact & Outcomes"
  },
  {
    id: 4,
    question: "How does this partnership align with the Mastercard Foundation's Young Africa Works strategy?",
    answer: "This partnership directly supports the Young Africa Works strategy by focusing on youth employment, entrepreneurship development, and economic empowerment across Africa. The project's emphasis on peer-to-peer learning and youth leadership development aligns perfectly with the Foundation's commitment to enabling young people to access dignified and fulfilling work.",
    category: "Partnership"
  },
  {
    id: 5,
    question: "How will the initiative measure its success, and what indicators will be used to track progress?",
    answer: "Success will be measured through various indicators including the number of youth trained and employed, business growth metrics for supported MSMEs, leadership development outcomes, and the successful transfer of program delivery to local youth organizations. Regular monitoring and evaluation will track both quantitative and qualitative progress indicators.",
    category: "Monitoring & Evaluation"
  },
  {
    id: 6,
    question: "What are the timelines of the Going Beyond Project?",
    answer: "The Going Beyond Project is designed as a five-year initiative, with progressive phases that gradually transfer responsibility to local youth leaders and organizations. The timeline includes initial setup and training phases, implementation and scaling phases, and final transition to youth-led program delivery.",
    category: "Timeline"
  },
  {
    id: 7,
    question: "Who are the participants in the project?",
    answer: "Participants include Youth Leaders who will be trained to deliver programs, Youth Peers who are young MSME owners receiving support, local Youth-Led Organizations (YLOs) and Youth-Serving Organizations (YSOs), and members of the Impact Makers Advisory Board who provide strategic guidance.",
    category: "Participants"
  },
  {
    id: 8,
    question: "How will the project engage with local Youth-Led Organizations (YLOs) and Youth-Serving Organizations (YSOs)?",
    answer: "The project will work closely with YLOs and YSOs as key implementation partners, providing them with capacity building support, resources, and training to effectively deliver programs in their communities. These organizations will play crucial roles in identifying participants, delivering services, and ensuring program sustainability.",
    category: "Partnerships"
  },
  {
    id: 9,
    question: "Who are the Youth Leaders in the Going Beyond Project?",
    answer: "Youth Leaders are carefully selected young individuals who will be trained to deliver DOT's programs in their communities. They serve as mentors, trainers, and program coordinators, playing a central role in the peer-to-peer learning model that is core to the project's approach.",
    category: "Youth Leaders"
  },
  {
    id: 10,
    question: "How will Youth Leaders be selected and trained for their roles within the project?",
    answer: "Youth Leaders will be selected through a comprehensive process that evaluates their leadership potential, community engagement, and commitment to youth development. Training will include program delivery methodologies, mentorship skills, business development knowledge, and ongoing support throughout their tenure.",
    category: "Youth Leaders"
  },
  {
    id: 11,
    question: "What opportunities are available for Youth Leaders to participate in the project?",
    answer: "Youth Leaders have opportunities to lead program delivery, mentor young entrepreneurs, participate in capacity building workshops, engage in peer-to-peer learning networks, and contribute to the strategic development of youth-led initiatives in their communities.",
    category: "Youth Leaders"
  },
  {
    id: 12,
    question: "Are Youth Leaders Paid?",
    answer: "Yes, Youth Leaders receive compensation for their roles in the project. The payment structure is designed to recognize their valuable contribution while ensuring the sustainability of the program model.",
    category: "Youth Leaders"
  },
  {
    id: 13,
    question: "Who are the Youth Peers in the Going Beyond Project?",
    answer: "Youth Peers are young MSME (Micro, Small, and Medium Enterprise) owners who participate in the project as beneficiaries. They receive support, training, and mentorship to grow their businesses and develop their entrepreneurial skills through the peer-to-peer learning model.",
    category: "Participants"
  },
  {
    id: 14,
    question: "What types of activities or opportunities are available for Youth Peers (young MSME owners) to participate in?",
    answer: "Youth Peers can participate in business development workshops, peer mentoring sessions, networking events, skills training programs, access to market linkages, and ongoing support for business growth and sustainability.",
    category: "Participants"
  },
  {
    id: 15,
    question: "What avenues are available for Youth Peers to access financial support or resources for their entrepreneurial ventures?",
    answer: "The project provides various pathways to financial support including connections to microfinance institutions, access to grant opportunities, business development support that improves creditworthiness, and linkages to impact investors and funding networks.",
    category: "Financial Support"
  },
  {
    id: 16,
    question: "What is the Impact Makers Advisory Board?",
    answer: "The Impact Makers Advisory Board is a strategic governance body composed of experienced professionals, youth representatives, and stakeholders who provide guidance, oversight, and strategic direction to ensure the project achieves its objectives and maintains its focus on youth empowerment.",
    category: "Governance"
  },
  {
    id: 17,
    question: "Are there any fees associated with the training programs?",
    answer: "No, the training programs provided through the Going Beyond project are free of charge for all participants. The project is designed to remove financial barriers and ensure equitable access to capacity building opportunities for young people.",
    category: "Program Details"
  },
  {
    id: 18,
    question: "How will the project ensure the inclusion and representation of marginalized or vulnerable youth?",
    answer: "The project has specific strategies to ensure inclusive participation, including targeted outreach to marginalized communities, accessibility accommodations, culturally sensitive programming, and partnerships with organizations that serve vulnerable youth populations.",
    category: "Inclusion"
  },
  {
    id: 19,
    question: "How will we safeguard the young people participating in the project?",
    answer: "Comprehensive safeguarding measures include background checks for all staff and volunteers, clear safeguarding policies and procedures, regular training on child protection, safe reporting mechanisms, and ongoing monitoring to ensure participant safety and wellbeing.",
    category: "Safeguarding"
  },
  {
    id: 20,
    question: "What role will technology play in supporting the project's objectives and activities?",
    answer: "Technology will be integral to program delivery, including digital learning platforms, mobile applications for peer networking, online mentorship tools, digital financial services integration, and data management systems for monitoring and evaluation.",
    category: "Technology"
  },
  {
    id: 21,
    question: "How will the project address challenges related to access to technology and digital literacy among youth participants?",
    answer: "The project includes digital literacy training components, partnerships with technology providers for device access, offline program alternatives, and gradual technology integration that builds participants' digital skills alongside program participation.",
    category: "Technology"
  },
  {
    id: 22,
    question: "How will the project foster collaboration and knowledge sharing among stakeholders?",
    answer: "Collaboration will be fostered through regular stakeholder meetings, knowledge sharing platforms, cross-country learning exchanges, joint planning sessions, and the creation of communities of practice that enable ongoing collaboration and learning.",
    category: "Collaboration"
  },
  {
    id: 23,
    question: "How can I find more information about Going Beyond?",
    answer: "For more information about the Going Beyond project, you can contact Avencion Limited directly through our website, attend our information sessions, follow our social media channels, or reach out to our project team for specific inquiries about participation or partnership opportunities.",
    category: "Information"
  }
];

const dotCategories = [
  "All",
  "Project Overview",
  "Impact & Outcomes",
  "Partnership",
  "Monitoring & Evaluation",
  "Timeline",
  "Youth Leaders",
  "Participants",
  "Partnerships",
  "Financial Support",
  "Technology",
  "Program Details",
  "Inclusion",
  "Safeguarding",
  "Governance",
  "Collaboration",
  "Information"
];

export default function DOTFAQPage() {
  const [activeCategory, setActiveCategory] = useState("All");
  const [openItems, setOpenItems] = useState<number[]>([1]); // First item open by default
  
  const heroRef = useRef(null);
  const faqRef = useRef(null);
  const heroInView = useInView(heroRef, { once: true });
  const faqInView = useInView(faqRef, { once: true, amount: 0.1, margin: "100px" });

  const filteredFAQs = activeCategory === "All" 
    ? dotFAQs 
    : dotFAQs.filter(faq => faq.category === activeCategory);

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div>
      <Navbar />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://i.imgur.com/Gb2KwNo.png"
            alt="DOT Project - Going Beyond FAQs"
            fill
            className="object-contain bg-gray-900"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"></div>
        </div>

        {/* Decorative Elements */}
        <div
          className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />
        <div
          className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
          style={{
            background:
              "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
          }}
        />

        <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-center"
          >
            <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
             Going Beyond - DOT Project FAQs
            </span>
            <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
              DOT Project{" "}
              <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
                FAQs
              </span>
            </h1>
            <p className="mx-auto max-w-4xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
              Learn more about the Going Beyond – Partnering for a Youth-Led Future project, 
              a transformative partnership between DOT and the Mastercard Foundation across four African countries.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
            >
              <Link
                href="#faqs"
                className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
              >
                Explore DOT FAQs
              </Link>
              <Link
                href="/faq/avencion"
                className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
              >
                Avencion FAQs
              </Link>
            </motion.div>

            {/* Project Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={heroInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              className="grid grid-cols-2 gap-8 md:grid-cols-4"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">4</div>
                <div className="text-sm text-gray-300 md:text-base">African Countries</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">5</div>
                <div className="text-sm text-gray-300 md:text-base">Year Project</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">Youth</div>
                <div className="text-sm text-gray-300 md:text-base">Led Future</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white md:text-4xl">DOT</div>
                <div className="text-sm text-gray-300 md:text-base">Partnership</div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faqs" ref={faqRef} className="relative w-full overflow-hidden py-12"
        style={{
          background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        }}>
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="mb-8 text-center"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
              Going Beyond - DOT Project FAQs
            </h2>
            <p className="mx-auto max-w-3xl text-lg text-gray-600">
              Find answers to common questions about the Going Beyond – Partnering for a Youth-Led Future project.
            </p>
          </motion.div>

          {/* Category Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
            className="mb-8 flex flex-wrap justify-center gap-2"
          >
            {dotCategories.map((category) => (
              <button
                key={category}
                type="button"
                onClick={() => setActiveCategory(category)}
                className={`rounded-full px-6 py-2 text-sm font-medium transition-all duration-300 ${
                  activeCategory === category
                    ? "bg-green-500 text-white shadow-lg"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {category}
              </button>
            ))}
          </motion.div>

          {/* FAQ Items */}
          <div className="mx-auto max-w-4xl">
            <div className="space-y-4">
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={faq.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={faqInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.4, ease: "easeOut" }}
                  className="relative overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-lg"
                >
                  <BorderBeam
                    duration={8 + index}
                    size={300}
                    className="from-transparent via-[#92b2f8]/40 to-transparent"
                  />

                  <button
                    type="button"
                    onClick={() => toggleItem(faq.id)}
                    className="w-full p-6 text-left transition-all duration-300 hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="mb-2 flex items-center gap-2">
                          <span className="rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-600">
                            {faq.category}
                          </span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 pr-4">
                          {faq.question}
                        </h3>
                      </div>
                      <div className="flex-shrink-0">
                        {openItems.includes(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-green-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                  </button>

                  {openItems.includes(faq.id) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className="border-t border-gray-100 px-6 pb-6"
                    >
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <Footer4Col />
    </div>
  );
}
