import {
  Dribbble,
  Facebook,
  Github,
  Instagram,
  Mail,
  MapPin,
  Phone,
  Twitter,
} from "lucide-react";
import Link from "next/link";

const socialLinks = [
  { icon: Facebook, label: "Facebook" },
  { icon: Instagram, label: "Instagram" },
  { icon: Twitter, label: "Twitter" },
  { icon: Github, label: "GitHub" },
  { icon: Dribbble, label: "Dribbble" },
];

const aboutLinks = [
  { text: "About Us", href: "/about" },
  { text: "Our Mission", href: "/about#mission" },
  { text: "Our Team", href: "/about#team" },
  { text: "Careers", href: "/careers" },
];

const serviceLinks = [
  { text: "ICT4D Solutions", href: "/projects" },
  { text: "Capacity Building", href: "/projects" },
  { text: "Strategic Partnerships", href: "/projects" },
  { text: "Data Management", href: "/projects" },
];

const helpfulLinks = [
  { text: "Projects", href: "/projects" },
  { text: "Get Started", href: "/get-started" },
  { text: "Contact", href: "/get-started" },
];



export default function Footer4Col() {
  return (
    <footer className="w-full place-self-end bg-gray-900 text-white">
      <div className="mx-auto max-w-screen-xl px-4 pb-6 pt-16 sm:px-6 lg:px-8 lg:pt-24">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          <div>
            <div className="flex justify-center gap-2 sm:justify-start">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-blue-700">
                <span className="text-sm font-bold text-white">A</span>
              </div>
              <span className="text-2xl font-semibold text-white">
                Avencion Limited
              </span>
            </div>

            <p className="mt-6 max-w-md text-center leading-relaxed text-gray-300 sm:max-w-xs sm:text-left">
              Give us a call or drop by anytime, we endeavour to answer all enquiries within 24 hours on business days. We will be happy to answer your questions.
            </p>

            <ul className="mt-8 flex justify-center gap-6 sm:justify-start md:gap-8">
              {socialLinks.map(({ icon: Icon, label }) => (
                <li key={label}>
                  <Link
                    href="#"
                    className="text-blue-400 transition hover:text-blue-300"
                  >
                    <span className="sr-only">{label}</span>
                    <Icon className="size-6" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4 lg:col-span-2">
            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-white">About Us</p>

              <ul className="mt-8 space-y-4 text-sm">
                {aboutLinks.map(({ text, href }) => (
                  <li key={text}>
                    <Link
                      className="text-gray-300 transition hover:text-white"
                      href={href}
                    >
                      {text}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-white">Our Services</p>

              <ul className="mt-8 space-y-4 text-sm">
                {serviceLinks.map(({ text, href }) => (
                  <li key={text}>
                    <Link
                      className="text-gray-300 transition hover:text-white"
                      href={href}
                    >
                      {text}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-white">Quick Links</p>

              <ul className="mt-8 space-y-4 text-sm">
                {helpfulLinks.map(({ text, href }) => (
                  <li key={text}>
                    <Link
                      href={href}
                      className="text-gray-300 transition hover:text-white"
                    >
                      {text}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-white">Contact Us</p>

              <div className="mt-8 space-y-6">
                {/* Zambia Office */}
                <div>
                  <h4 className="font-medium text-blue-400 mb-2">Zambia Office</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-center gap-1.5 sm:justify-start">
                      <MapPin className="size-4 shrink-0 text-blue-400" />
                      <address className="not-italic text-gray-300">
                        2 Chifumbule Road, Woodlands, Lusaka Zambia
                      </address>
                    </div>
                    <div className="flex items-center justify-center gap-1.5 sm:justify-start">
                      <Phone className="size-4 shrink-0 text-blue-400" />
                      <span className="text-gray-300">+260 960 638 188</span>
                    </div>
                  </div>
                </div>

                {/* USA Office */}
                <div>
                  <h4 className="font-medium text-blue-400 mb-2">USA Office</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-center gap-1.5 sm:justify-start">
                      <MapPin className="size-4 shrink-0 text-blue-400" />
                      <address className="not-italic text-gray-300">
                        7557 Main Street Suite 601, Houston, TX 77030
                      </address>
                    </div>
                    <div className="flex items-center justify-center gap-1.5 sm:justify-start">
                      <Phone className="size-4 shrink-0 text-blue-400" />
                      <span className="text-gray-300">****** 676 7171</span>
                    </div>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-center justify-center gap-1.5 sm:justify-start">
                  <Mail className="size-4 shrink-0 text-blue-400" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-gray-700 pt-6">
          <div className="text-center sm:flex sm:justify-between sm:text-left">
            <p className="text-sm text-gray-300">
              <span className="block sm:inline">All rights reserved.</span>
            </p>

            <p className="mt-4 text-sm text-gray-300 transition sm:order-first sm:mt-0">
              &copy; 2025 Avencion Limited
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
