"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

export default function Globe3D() {
  return (
    <section className="relative w-full overflow-hidden min-h-screen flex items-center justify-center font-light text-white antialiased">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=1920&h=1080&fit=crop"
          alt="Team collaboration and partnership - Avencion Limited"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>
      </div>

      {/* Decorative Elements */}
      <div
        className="absolute right-0 top-0 h-1/2 w-1/2 z-10"
        style={{
          background:
            "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
        }}
      />
      <div
        className="absolute left-0 top-0 h-1/2 w-1/2 -scale-x-100 z-10"
        style={{
          background:
            "radial-gradient(circle at 70% 30%, rgba(146, 178, 248, 0.2) 0%, rgba(146, 178, 248, 0) 60%)",
        }}
      />

      <div className="container relative z-20 mx-auto max-w-2xl px-4 text-center md:max-w-4xl md:px-6 lg:max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="text-center"
        >
          <span className="mb-6 inline-block rounded-full border border-[#92b2f8]/30 px-4 py-2 text-sm text-white bg-blue-500/20 backdrop-blur-sm">
           Since 2010 - Transforming Africa Through Partnerships
          </span>
          <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl lg:text-7xl">
            Empowering Organizations Through{" "}
            <span className="bg-gradient-to-r from-[#92b2f8] to-[#6366f1] bg-clip-text text-transparent">
              Strategic Partnerships
            </span>
          </h1>
          <p className="mx-auto max-w-3xl text-lg text-gray-200 md:text-xl lg:text-2xl leading-relaxed mb-8">
            We design and implement high impact solutions for governments, businesses, and communities across Africa.
            Our expertise spans health, sustainable energy, technology, infrastructure, agriculture, education and government sectors.
          </p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            className="flex flex-col items-center justify-center gap-4 sm:flex-row mb-16"
          >
            <Link
              href="/get-started"
              className="relative w-full overflow-hidden rounded-full border border-[#92b2f8] bg-blue-500 px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-[#92b2f8]/90 hover:shadow-[0_0_20px_rgba(146, 178, 248, 0.5)] sm:w-auto"
            >
              Get Started
            </Link>
            <Link
              href="/projects"
              className="relative w-full overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-8 py-4 text-white shadow-lg transition-all duration-300 hover:bg-white/20 sm:w-auto"
            >
              View Our Work
            </Link>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
            className="grid grid-cols-2 gap-8 md:grid-cols-4"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-white md:text-4xl">350+</div>
              <div className="text-sm text-gray-300 md:text-base">Trusted Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white md:text-4xl">215+</div>
              <div className="text-sm text-gray-300 md:text-base">Completed Projects</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white md:text-4xl">15+</div>
              <div className="text-sm text-gray-300 md:text-base">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white md:text-4xl">2</div>
              <div className="text-sm text-gray-300 md:text-base">Regional Offices</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
